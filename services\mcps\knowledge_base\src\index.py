import uuid
from typing import Dict, Any
from qdrant_client import Qdrant<PERSON>lient
from qdrant_client.models import Distance, VectorParams, PointStruct
from src.embed import Embedder, EMBEDDING_DIMENSION_DICT
from src.config.setup import settings

class Indexer:
  def __init__(self):
    self.embedder = Embedder()
    self.client = QdrantClient(
      host=settings.QDRANT_HOST,
      port=settings.QDRANT_PORT
    )
    self.collection_name = settings.QDRANT_COLLECTION_NAME
    self._ensure_collection_exists()

  def _ensure_collection_exists(self):
    """Create collection if it doesn't exist"""
    try:
      self.client.get_collection(self.collection_name)
    except Exception:
      embedding_dim = EMBEDDING_DIMENSION_DICT.get(settings.EMBEDDING_MODEL_NAME)
      self.client.create_collection(
        collection_name=self.collection_name,
        vectors_config=VectorParams(size=embedding_dim, distance=Distance.COSINE)
      )

  def index(self, text: str | list[str], metadata: Dict[str, Any] = None) -> str:
    """Index text or list of texts into Qdrant"""
    if isinstance(text, str):
      texts = [text]
    else:
      texts = text

    points = []
    for i, doc_text in enumerate(texts):
      # Generate embedding
      embedding = self.embedder.embed(doc_text)
      
      # Create point with metadata
      point_id = str(uuid.uuid4())
      payload = {
        "text": doc_text,
        "index": i,
        **(metadata or {})
      }
      
      points.append(PointStruct(
        id=point_id,
        vector=embedding,
        payload=payload
      ))

    # Insert into Qdrant
    self.client.upsert(
      collection_name=self.collection_name,
      points=points
    )
    
    return f"Indexed {len(texts)} documents"

if __name__ == "__main__":
  indexer = Indexer()
  result = indexer.index("Hello, world! My name is Kristian and im part of the Microsoft AI Agents hackathon!")
  print(result)