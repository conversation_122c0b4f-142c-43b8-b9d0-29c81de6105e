from __future__ import annotations

from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from agents import Agent

from src.config.modelling import reasoning_model, regular_model, small_model
from src.tools.mcps import exa_mcp, knowledge_base_mcp


# ** Search Agent responsible for doing extensive searches based on a user's query.

search_agent_instructions = """
You are a helpful agent responsible for doing extensive searches based on a user's query.
you deeply consider the context of the user's query and tries to find the most relevant information to answer the query.
The tools you have at your disposal are exa (a tool to search the web) and a knowledge base tool (to search the knowledge base).

You will return a well thought out answer to the user's query along with the search results that you have used to answer the query.
"""

class WebSearchResults(BaseModel):
  cited_text: str
  source_url: str

class KnowledgeBaseResult(BaseModel):
  text: str
  metadata: Optional[Dict[str, Any]] = None

class SearchOutput(BaseModel):
  web_search_results: Optional[List[WebSearchResults]] = None
  knowledge_base_results: Optional[List[KnowledgeBaseResult]] = None

class SearchAgentResponse(BaseModel):
  search_results: Optional[SearchOutput] = None
  final_answer: str

search_agent = Agent(
  name="search_agent",
  model=reasoning_model,
  instructions=search_agent_instructions,
  output_type=SearchAgentResponse,
  mcp_servers=[exa_mcp, knowledge_base_mcp]
)



__all__ = ["search_agent"]