from typing import List, Dict, Any
from dataclasses import dataclass
from qdrant_client import QdrantClient
from src.embed import Embedder
from src.config.setup import settings

@dataclass(frozen=True)
class SearchResult:
  """Internal search result - fast, immutable"""
  text: str
  score: float
  metadata: Dict[str, Any]

class Retriever:
  def __init__(self):
    self.embedder = Embedder()
    self.client = QdrantClient(
      host=settings.QDRANT_HOST,
      port=settings.QDRANT_PORT
    )
    self.collection_name = settings.QDRANT_COLLECTION_NAME

  def retrieve(self, query: str, limit: int = 5, score_threshold: float = 0.7) -> List[Dict[str, Any]]:
    """Retrieve relevant documents based on semantic similarity"""
    try:
      # Generate query embedding
      query_embedding = self.embedder.embed(query)
      
      # Search in Qdrant
      search_results = self.client.search(
        collection_name=self.collection_name,
        query_vector=query_embedding,
        limit=limit,
        score_threshold=score_threshold
      )
      
      # Convert to internal dataclass first (fast processing)
      internal_results = [
        SearchResult(
          text=result.payload.get("text", ""),
          score=result.score,
          metadata={k: v for k, v in result.payload.items() if k != "text"}
        )
        for result in search_results
      ]
      
      # Convert to dict for API response (when needed)
      return [
        {
          "text": result.text,
          "score": result.score,
          "metadata": result.metadata
        }
        for result in internal_results
      ]
      
    except Exception as e:
      print(f"Error during retrieval: {e}")
      return []

if __name__ == "__main__":
  retriever = Retriever()
  results = retriever.retrieve("Microsoft AI hackathon")
  print(f"Found {len(results)} results:")
  for result in results:
    print(f"Score: {result['score']:.3f} - {result['text'][:100]}...")