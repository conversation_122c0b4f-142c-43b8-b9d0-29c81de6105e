import asyncio
import os
import sys

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.index import Indexer
from src.config.setup import settings

def get_sample_documents():
  """Return sample documents for the Microsoft AI Agents Hackathon"""
  return [
    {
      "text": "Microsoft AI Agents Hackathon is a competition focused on building intelligent agents using Microsoft's AI technologies. Participants create innovative solutions using Azure OpenAI, Azure AI services, and other Microsoft AI tools.",
      "metadata": {"source": "hackathon_info", "category": "event", "topic": "microsoft_hackathon"}
    },
    {
      "text": "FastMCP is a Python framework for building Model Context Protocol (MCP) servers and clients. It provides a simple way to create tools that AI agents can use to interact with external systems and data sources.",
      "metadata": {"source": "technology", "category": "framework", "topic": "fastmcp"}
    },
    {
      "text": "Qdrant is a vector database designed for storing and searching high-dimensional vectors. It's commonly used in RAG (Retrieval Augmented Generation) systems to store document embeddings for semantic search.",
      "metadata": {"source": "technology", "category": "database", "topic": "qdrant"}
    },
    {
      "text": "Retrieval Augmented Generation (RAG) is a technique that combines information retrieval with language generation. It allows AI models to access external knowledge by retrieving relevant documents and using them to generate more accurate responses.",
      "metadata": {"source": "ai_concepts", "category": "technique", "topic": "rag"}
    },
    {
      "text": "Azure OpenAI Service provides REST API access to OpenAI's powerful language models including GPT-4, GPT-3.5, and embedding models. It offers enterprise-grade security and compliance features for business applications.",
      "metadata": {"source": "microsoft_services", "category": "api", "topic": "azure_openai"}
    },
    {
      "text": "Model Context Protocol (MCP) is a standard for connecting AI assistants with external tools and data sources. It enables agents to interact with databases, APIs, and other services in a standardized way.",
      "metadata": {"source": "protocols", "category": "standard", "topic": "mcp"}
    },
    {
      "text": "Docker Compose is a tool for defining and running multi-container Docker applications. It uses a YAML file to configure application services, networks, and volumes, making it easy to deploy complex microservice architectures.",
      "metadata": {"source": "technology", "category": "deployment", "topic": "docker"}
    },
    {
      "text": "Semantic search uses machine learning to understand the meaning and context of search queries, rather than just matching keywords. It's powered by embedding models that convert text into high-dimensional vectors.",
      "metadata": {"source": "ai_concepts", "category": "search", "topic": "semantic_search"}
    }
  ]

async def main():
  """Index sample documents into the knowledge base"""
  print("🚀 Starting manual indexing for Microsoft AI Agents Hackathon")
  print(f"🔗 Connecting to Qdrant at {settings.QDRANT_HOST}:{settings.QDRANT_PORT}")
  print(f"📊 Collection: {settings.QDRANT_COLLECTION_NAME}")
  
  try:
    indexer = Indexer()
    documents = get_sample_documents()
    
    print(f"📚 Indexing {len(documents)} sample documents...")
    
    for i, doc in enumerate(documents, 1):
      result = indexer.index(doc["text"], doc["metadata"])
      print(f"  {i}. {result}")
    
    print(f"\n✅ Successfully indexed {len(documents)} documents!")
    print("🔍 You can now test search queries using the knowledge base MCP tools")
    
  except Exception as e:
    print(f"❌ Error during indexing: {e}")
    print(f"\n🔧 Troubleshooting:")
    print(f"   Current Qdrant config: {settings.QDRANT_HOST}:{settings.QDRANT_PORT}")
    print(f"   1. Make sure Qdrant is running: docker-compose up qdrant_db")
    print(f"   2. Check if Qdrant is accessible: curl http://localhost:7004/collections")
    print(f"   3. Verify your .env file has correct Azure OpenAI credentials")
    return 1
    
  return 0

if __name__ == "__main__":
  exit_code = asyncio.run(main())
  sys.exit(exit_code) 