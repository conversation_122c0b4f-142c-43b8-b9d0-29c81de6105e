from src.config.setup import settings
from openai import AzureOpenAI

EMBEDDING_DIMENSION_DICT = {
  'text-embedding-3-large': 3072,
}

class Embedder:
  def __init__(self):
    self.client = AzureOpenAI(
      api_version="2024-12-01-preview",
      azure_endpoint=settings.AZURE_FOUNDRY_EMBEDDING_INFERENCE_ENDPOINT,
      api_key=settings.AZURE_FOUNDRY_API_KEY
    )
    self.model = settings.EMBEDDING_MODEL_NAME

  def embed(self, text: str | list[str]) -> list[float]: return self.client.embeddings.create(input=text, model=self.model).data[0].embedding


__all__ = ["Embedder", "EMBEDDING_DIMENSION_DICT"]

if __name__ == "__main__":
  embedder = Embedder()
  emb = embedder.embed("Hello, world!")
  print(emb) # great it works!
  print(len(emb)) # 3072