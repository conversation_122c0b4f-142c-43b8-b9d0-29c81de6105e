from __future__ import annotations

import asyncio
from agents import <PERSON>, RunResult, trace

from src.flows.agent_hub import search_agent, SearchAgentResponse
from src.config.tracers import setup_trace_processors
from src.tools.mcps import exa_mcp, knowledge_base_mcp


async def run_rag_flow(user_query: str) -> str:
  """
  Run the RAG flow for a user query.
  """
  
  with trace(workflow_name="rag_flow"):
    agent_response: RunResult = await Runner.run(search_agent, input=user_query)
    search_out: SearchAgentResponse = agent_response.final_output
    print(f"Search Agent Response: {search_out}")
    return search_out.final_answer

async def main():
  setup_trace_processors()
  async with exa_mcp, knowledge_base_mcp:
    result = await run_rag_flow("What is the capital of France?")
    print(f"Search Agent Response: {result}")

if __name__ == "__main__":
  asyncio.run(main())