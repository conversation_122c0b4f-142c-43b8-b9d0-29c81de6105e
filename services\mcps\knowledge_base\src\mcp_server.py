from typing import List, Dict, Any, Optional, Annotated
from pydantic import Field
from fastmcp import FastMCP
from src.index import Indexer
from src.retrieve import Retriever

# Initialize FastMCP server
mcp = FastMCP("Knowledge Base RAG Server")

# Initialize components
indexer = Indexer()
retriever = Retriever()

@mcp.tool
def index_processed_chunks(
  chunks: Annotated[
    List[Dict[str, Any]],
    Field(description="List of processed chunks from document processor, each containing 'text', 'metadata', 'chunk_id', and 'source_id'")
  ]
) -> str:
  """
  Index pre-processed document chunks into the knowledge base.
  
  This tool is optimized for chunks that have already been processed by the
  document processor service. Each chunk should contain structured metadata.
  """
  try:
    indexed_count = 0
    
    for chunk in chunks:
      if "error" in chunk:
        continue  # Skip error chunks
        
      text = chunk.get("text", "")
      metadata = chunk.get("metadata", {})
      
      if text.strip():  # Only index non-empty chunks
        indexer.index(text, metadata)
        indexed_count += 1
    
    return f"Successfully indexed {indexed_count} processed chunks"
    
  except Exception as e:
    return f"Error indexing processed chunks: {str(e)}"

@mcp.tool
def index_simple_text(
  text: Annotated[
    str | List[str], 
    Field(description="Simple text document(s) to index directly without processing", min_length=1)
  ],
  metadata: Annotated[
    Optional[Dict[str, Any]], 
    Field(description="Optional metadata to attach to the documents (e.g., source, timestamp, category)", default=None)
  ] = None
) -> str:
  """
  Index simple text documents directly into the knowledge base.
  
  For quick indexing of short texts that don't need chunking or processing.
  For larger documents, use the document processor service first.
  """
  try:
    result = indexer.index(text, metadata)
    return result
  except Exception as e:
    return f"Error indexing documents: {str(e)}"

@mcp.tool
def search_knowledge_base(
  query: Annotated[
    str, 
    Field(description="Natural language search query to find relevant documents", min_length=1)
  ],
  limit: Annotated[
    int, 
    Field(description="Maximum number of results to return", ge=1, le=50, default=5)
  ] = 5,
  score_threshold: Annotated[
    float, 
    Field(description="Minimum similarity score threshold (0.0-1.0). Higher values return more relevant results, try lower values if you want more results", ge=0.0, le=1.0, default=0.7)
  ] = 0.7
) -> List[Dict[str, Any]]:
  """
  Search the knowledge base for documents relevant to the query.
  
  Uses semantic similarity to find the most relevant documents based on
  the query text. Returns documents with similarity scores and metadata.
  """
  try:
    results = retriever.retrieve(query, limit, score_threshold)
    return results
  except Exception as e:
    return [{"error": f"Error searching knowledge base: {str(e)}"}]

@mcp.tool
def get_knowledge_base_info() -> Dict[str, Any]:
  """
  Get current status and statistics about the knowledge base.
  
  Returns information about the Qdrant collection including document count,
  vector count, and overall health status.
  """
  try:
    collection_info = retriever.client.get_collection(retriever.collection_name)
    return {
      "collection_name": retriever.collection_name,
      "points_count": collection_info.points_count,
      "vectors_count": collection_info.vectors_count,
      "status": "healthy"
    }
  except Exception as e:
    return {
      "status": "error",
      "error": str(e)
    }

if __name__ == "__main__":
  # Support both STDIO and SSE transports
  import sys
  if len(sys.argv) > 1 and sys.argv[1] == "--sse":
    mcp.run(transport="sse", host="0.0.0.0", port=9000)
  else:
    mcp.run(transport="stdio") 