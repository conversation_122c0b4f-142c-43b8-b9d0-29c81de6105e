from fastmcp import <PERSON>MC<PERSON>, Context
from exa_py import Exa

from typing import Annotated
from pydantic import Field

from src.setup import settings


exa = Exa(api_key=settings.EXA_API_KEY)

mcp = FastMCP(name="ExaSearch", stateless_http=True)

@mcp.tool(
  name="exa_search_web",
  description="Search the web for a query and get a detailed list of search results including the content of the page of each result.",
  tags=["search", "web"]
)
async def search_web(
  query: Annotated[str, Field(description="The query to search for")], 
  k: Annotated[int, Field(description="The number of results to return")], 
  start_published_date: Annotated[str, Field(description="The start date to search for (fx. 2025-05-09)")] = None, 
  end_published_date: Annotated[str, Field(description="The end date to search for (fx. 2025-05-10)")] = None, 
  ctx: Context | None = None) -> list[dict]:
  """
  Uses Exa to search the web for a query and get a detailed list of search results including the content of the page of each result.
  """
  
  ctx.info(f"Searching for {query} ...\nwith {k} results,\nwithin range: from {start_published_date} to {end_published_date}")
  ctx.report_progress(progress=50, total=100)
  results = exa.search_and_contents(
    query = query,
    start_published_date = start_published_date,
    end_published_date = end_published_date,
    num_results = k
  )
  ctx.report_progress(progress=100, total=100)
  return results


@mcp.tool(
  name="exa_get_contents",
  description="Fetches the full cleaned text for each URL in the list",
  tags=["fetch", "extract html"]
)
async def get_contents(
  urls: Annotated[list[str], Field(description="The list of URLs to fetch the contents of")], 
  ctx: Context | None = None) -> list[dict]:
  """
  Fetches the full cleaned text for each URL in the list.
  """
  ctx.info(f"Fetching contents for {len(urls)} URLs")
  ctx.report_progress(progress=50, total=100)
  results = exa.get_contents(urls)
  ctx.report_progress(progress=100, total=100)
  return results


if __name__ == "__main__":
  # Expose SSE on 0.0.0.0:9000  (FastMCP CLI handles uvicorn+SSE)
  mcp.run(host="0.0.0.0", port=9000, transport="sse")