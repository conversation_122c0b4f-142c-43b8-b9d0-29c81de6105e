from agents.extensions.models.litellm_model import LitellmModel

from src.config.setup import settings

# ** Do this if you dont want to use litellm as an llm proxy
# from agents import AsyncOpenAI, OpenAIChatCompletionsModel
# azure_client = AsyncOpenAI(api_key=settings.W_OPENAI_API_KEY, base_url=settings.W_OPENAI_BASE_URL) 

REASONING_MODEL_NAME = "azure/o3"
SMALL_MODEL_NAME = "azure/gpt-4.1-nano"
REGULAR_MODEL_NAME = "azure/gpt-4o"

# ** I prefer using litellm proxy with the openai agents sdk as it is more flexible and will be responsible for all llm providers
# litellm does have some flaws and is not the best option for all use cases
# but you can use it is a microservice for all LLM calls which is a nice thing.
reasoning_model = LitellmModel(
  model=REASONING_MODEL_NAME,
  base_url=settings.LLM_PROXY_BASE_URL,
  api_key="dummy"
)

small_model = LitellmModel(
  model=SMALL_MODEL_NAME,
  base_url=settings.LLM_PROXY_BASE_URL,
  api_key="dummy"
)

regular_model = LitellmModel(
  model=REGULAR_MODEL_NAME,
  base_url=settings.LLM_PROXY_BASE_URL,
  api_key="dummy"
)

__all__ = ["reasoning_model", "small_model", "regular_model"]