model_list:
  # AZURE AI FOUNDRY MODELS
  - model_name: gpt-4.1-nano
    litellm_params:
      model: azure/gpt-4.1-nano
      api_base: "os.environ/AZURE_API_BASE"
      api_key: "os.environ/AZURE_API_KEY"
      api_version: "2024-12-01-preview" # [OPTIONAL] litellm uses the latest azure api_version by default
  - model_name: gpt-4o
    litellm_params:
      model: azure/gpt-4o
      api_base: "os.environ/AZURE_API_BASE"
      api_key: "os.environ/AZURE_API_KEY"
      api_version: "2024-12-01-preview" # [OPTIONAL] litellm uses the latest azure api_version by default
  - model_name: o3
    litellm_params:
      model: azure/o3
      api_base: "os.environ/AZURE_API_BASE"
      api_key: "os.environ/AZURE_API_KEY"
      api_version: "2024-12-01-preview" # [OPTIONAL] litellm uses the latest azure api_version by default
  - model_name: o4-mini
    litellm_params:
      model: azure/o4-mini
      api_base: "os.environ/AZURE_API_BASE"
      api_key: "os.environ/AZURE_API_KEY"
      api_version: "2024-12-01-preview" # [OPTIONAL] litellm uses the latest azure api_version by default
