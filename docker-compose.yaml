networks:
  agent_network:
    external: false

services:
  rag_agent:
    build:
      context: .
      dockerfile: services/agent/Dockerfile
    networks:
      - agent_network
    env_file:
      - ./services/agent/.env
    ports:
      - "7001:8000"
    depends_on:
      exa_mcp:
        condition: service_started
      litellm_proxy:
        condition: service_started
      knowledge_base:
        condition: service_started

  knowledge_base:
    build:
      context: .
      dockerfile: services/mcps/knowledge_base/Dockerfile
    networks:
      - agent_network
    env_file:
      - ./services/mcps/knowledge_base/.env
    ports:
      - "7006:9000"
    depends_on:
      qdrant_db:
        condition: service_started

  # MCPs
  exa_mcp:
    build:
      context: .
      dockerfile: services/mcps/exa/Dockerfile
    networks:
      - agent_network
    ports:
      - "7002:9000"
    env_file:
      - ./services/mcps/exa/.env

  qdrant_db:
    image: qdrant/qdrant
    networks:
      - agent_network
    ports:
      - "7004:6333"
      - "7005:6334"
    volumes:
      - ./data/qdrant/qdrant_storage:/qdrant/storage

  litellm_proxy:
    image: ghcr.io/berriai/litellm:main-latest
    networks:
      - agent_network
    ports:
      - "7003:8000"
    env_file:
      - ./services/litellm_proxy/.env
    volumes:
      - ./services/litellm_proxy/litellm_config.yaml:/app/config.yaml
    command: ["--port", "8000", "--config", "/app/config.yaml"]
    restart: unless-stopped