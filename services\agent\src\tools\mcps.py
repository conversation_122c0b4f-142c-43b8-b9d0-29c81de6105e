from agents.mcp import MCPServerSse, MCPServerSseParams
from src.config.setup import settings

# Exa MCP
exa_mcp_params = MCPServerSseParams(
  url=settings.EXA_MCP_URL,
  headers={},
)

exa_mcp = MCPServerSse(
  params=exa_mcp_params,
  name="Exa",
  client_session_timeout_seconds=15
)

# allow you to take an MCP and only provide selected tools to the LLM
class ConstrainedMCPServer(MCPServerSse):
  def __init__(self, params: MCPServerSseParams, name: str, **kwargs):
    super().__init__(params=params, name=name, **kwargs)

  async def list_tools(self):
    tools = await super().list_tools()
    return [tool for tool in tools if tool.name.startswith("search") or tool.name.startswith("get_")]

  
knowledge_base_mcp_params = MCPServerSseParams(
  url=settings.KNOWLEDGE_BASE_MCP_URL,
  headers={},
)

knowledge_base_mcp = ConstrainedMCPServer(
  params=knowledge_base_mcp_params,
  name="Knowledge Base",
  client_session_timeout_seconds=15
)

__all__ = ["exa_mcp", "knowledge_base_mcp"]