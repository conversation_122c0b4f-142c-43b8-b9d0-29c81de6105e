# Knowledge Base RAG Service

A simple but powerful Retrieval Augmented Generation (RAG) system built with FastMCP and Qdrant for the Microsoft AI Agents Hackathon.

## Features

- **Semantic Indexing**: Index documents with Azure OpenAI embeddings
- **Vector Search**: Fast similarity search using Qdrant vector database  
- **MCP Integration**: Exposed as Model Context Protocol tools via FastMCP
- **Metadata Support**: Attach custom metadata to indexed documents
- **Docker Ready**: Containerized for easy deployment

## Architecture

```
┌─────────────────┐    ┌──────────────┐    ┌─────────────┐
│   MCP Client    │───▶│  FastMCP     │───▶│   Qdrant    │
│   (Agent)       │    │  Server      │    │  Database   │
└─────────────────┘    └──────────────┘    └─────────────┘
                              │
                              ▼
                       ┌──────────────┐
                       │ Azure OpenAI │
                       │  Embeddings  │
                       └──────────────┘
```

## Available MCP Tools

### `index_documents`
Index text documents into the knowledge base.

**Parameters:**
- `text`: str | List[str] - Single text or list of texts to index
- `metadata`: Dict[str, Any] (optional) - Metadata to attach

**Returns:** Success message with count of indexed documents

### `search_knowledge_base`  
Search for relevant documents using semantic similarity.

**Parameters:**
- `query`: str - Search query text
- `limit`: int (default: 5) - Maximum results to return
- `score_threshold`: float (default: 0.7) - Minimum similarity score

**Returns:** List of relevant documents with scores and metadata

### `get_knowledge_base_info`
Get information about the knowledge base status.

**Returns:** Collection info including document count and health status

## Setup

1. **Environment Variables**: Create `.env` file:
```bash
# Embedding Model Configuration
EMBEDDING_MODEL_NAME=text-embedding-3-large
AZURE_FOUNDRY_EMBEDDING_INFERENCE_ENDPOINT=your_endpoint
AZURE_FOUNDRY_API_KEY=your_api_key

# Qdrant Configuration (defaults should work with docker-compose)
QDRANT_HOST=qdrant
QDRANT_PORT=6333
QDRANT_COLLECTION_NAME=knowledge_base
```

2. **Run with Docker Compose**:
```bash
docker-compose up knowledge_base qdrant
```

## Usage Examples

### As MCP Tool (via Client)
```python
from fastmcp import Client

async with Client("python services/knowledge_base/src/mcp_server.py") as client:
  # Index documents
  await client.call_tool("index_documents", {
    "text": ["Document 1", "Document 2"],
    "metadata": {"source": "wiki"}
  })
  
  # Search
  results = await client.call_tool("search_knowledge_base", {
    "query": "search query",
    "limit": 5
  })
```

### Direct Usage
```python
from src.index import Indexer
from src.retrieve import Retriever

# Index documents
indexer = Indexer()
indexer.index("Your document text here")

# Retrieve relevant docs
retriever = Retriever()  
results = retriever.retrieve("search query")
```

## Integration with Agents

The knowledge base can be integrated with any agent that supports MCP:

1. **Add to Agent's MCP Configuration**:
```yaml
mcpServers:
  knowledge_base:
    command: "python"
    args: ["services/knowledge_base/src/mcp_server.py"]
```

2. **Use in Agent Code**:
```python
# Index new information
await agent.call_tool("index_documents", {
  "text": "New information to remember",
  "metadata": {"timestamp": "2024-01-01"}
})

# Search when answering questions
results = await agent.call_tool("search_knowledge_base", {
  "query": user_question
})
```

## Development

- **Test**: `python test_rag.py`
- **Standalone Index**: `python -m src.index`
- **Standalone Retrieve**: `python -m src.retrieve`  
- **MCP Server**: `python -m src.mcp_server`

Built with ❤️ for the Microsoft AI Agents Hackathon 