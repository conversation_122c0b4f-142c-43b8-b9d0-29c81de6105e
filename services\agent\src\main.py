from __future__ import annotations

from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

from src.flows.rag_flow import run_rag_flow
from src.config.tracers import setup_trace_processors
from src.config.setup import settings
from src.tools.mcps import exa_mcp, knowledge_base_mcp

class QueryRequest(BaseModel):
  query: str

class QueryResponse(BaseModel):
  answer: str
  metadata: Dict[str, Any] = {}

@asynccontextmanager
async def lifespan(app: FastAPI):
  """Application lifespan manager for MCP connections"""
  setup_trace_processors()
  
  # Start MCP connections
  await exa_mcp.__aenter__()
  await knowledge_base_mcp.__aenter__()
  
  yield
  
  # Clean up MCP connections
  await exa_mcp.__aexit__(None, None, None)
  await knowledge_base_mcp.__aexit__(None, None, None)

# Create FastAPI app
app = FastAPI(
  title="RAG Agent API",
  description="Microsoft AI Agents Hackathon - RAG Agent Service",
  version="1.0.0",
  lifespan=lifespan
)

@app.get("/")
async def root():
  """Health check endpoint"""
  return {
    "message": "RAG Agent API is running!",
    "service": settings.APPLICATION_NAME,
    "status": "healthy"
  }

@app.get("/health")
async def health():
  """Detailed health check"""
  try:
    # Test MCP connections
    exa_tools = await exa_mcp.list_tools()
    kb_tools = await knowledge_base_mcp.list_tools()
    
    return {
      "status": "healthy",
      "service": settings.APPLICATION_NAME,
      "mcp_connections": {
        "exa": {"status": "connected", "tools_count": len(exa_tools)},
        "knowledge_base": {"status": "connected", "tools_count": len(kb_tools)}
      }
    }
  except Exception as e:
    raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")

@app.post("/query", response_model=QueryResponse)
async def query_agent(request: QueryRequest) -> QueryResponse:
  """
  Send a query to the RAG agent and get a response.
  
  The agent will search both the knowledge base and the web to provide
  a comprehensive answer to your query.
  """
  try:
    if not request.query.strip():
      raise HTTPException(status_code=400, detail="Query cannot be empty")
    
    # Run the RAG flow
    result = await run_rag_flow(request.query)
    
    return QueryResponse(
      answer=result,
      metadata={
        "query": request.query,
        "service": settings.APPLICATION_NAME
      }
    )
    
  except Exception as e:
    raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

@app.get("/mcp/tools")
async def list_mcp_tools():
  """List available MCP tools"""
  try:
    exa_tools = await exa_mcp.list_tools()
    kb_tools = await knowledge_base_mcp.list_tools()
    
    return {
      "exa_tools": [{"name": tool.name, "description": tool.description} for tool in exa_tools],
      "knowledge_base_tools": [{"name": tool.name, "description": tool.description} for tool in kb_tools]
    }
  except Exception as e:
    raise HTTPException(status_code=500, detail=f"Error listing tools: {str(e)}")

if __name__ == "__main__":
  import uvicorn
  uvicorn.run(app, host="0.0.0.0", port=8000) 